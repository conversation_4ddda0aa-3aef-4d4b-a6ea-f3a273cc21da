2025-10-07 21:20:34,204 4824 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\odoo\\addons', skipped 
2025-10-07 21:20:34,205 4824 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\addons', skipped 
2025-10-07 21:20:34,205 4824 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\custom_addons', skipped 
2025-10-07 21:20:34,205 4824 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 21:20:34,207 4824 INFO ? odoo: Odoo version 19.0 
2025-10-07 21:20:34,207 4824 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 21:20:34,208 4824 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons']) 
2025-10-07 21:20:34,208 4824 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 21:20:34,559 4824 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 21:20:35,653 4824 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 21:20:35,661 4824 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 21:21:29,010 23424 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\odoo\\addons', skipped 
2025-10-07 21:21:29,010 23424 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\addons', skipped 
2025-10-07 21:21:29,011 23424 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\custom_addons', skipped 
2025-10-07 21:21:29,011 23424 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 21:21:29,012 23424 INFO ? odoo: Odoo version 19.0 
2025-10-07 21:21:29,012 23424 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 21:21:29,012 23424 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons']) 
2025-10-07 21:21:29,013 23424 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 21:21:29,553 23424 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 21:21:30,366 23424 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 21:21:30,376 23424 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 21:21:33,955 23424 INFO ? odoo.modules.loading: loading 1 modules... 
2025-10-07 21:21:33,961 23424 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-10-07 21:21:33,977 23424 INFO ? odoo.modules.loading: loading 14 modules... 
2025-10-07 21:21:34,665 23424 INFO ? odoo.modules.loading: 14 modules loaded in 0.69s, 0 queries (+0 extra) 
2025-10-07 21:21:34,713 23424 INFO ? odoo.modules.loading: Modules loaded. 
2025-10-07 21:21:34,735 23424 INFO ? odoo.registry: Registry loaded in 0.864s 
2025-10-07 21:21:34,736 23424 INFO school odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-10-07 21:21:34,788 23424 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:21:34] "POST /web/webclient/version_info HTTP/1.1" 200 - 20 0.057 0.969
